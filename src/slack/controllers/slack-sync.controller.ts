import {
  Controller,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import { GetBotCtx } from '../../auth/decorators';
import { AuthGuard } from '../../auth/guards';
import { BotCtx } from '../../auth/interfaces';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import {
  SlackExternalUsersSyncJob,
  SlackUsersSyncJob,
} from '../processors/jobs';

export enum SlackSyncType {
  EXTERNAL_USERS = 'external-users',
  INTERNAL_USERS = 'internal-users',
}

@UseGuards(AuthGuard)
@Controller('v1/slack/sync')
export class SlackSyncController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Jobs
    private readonly slackExternalUsersSyncJob: SlackExternalUsersSyncJob,
    private readonly slackUsersSyncJob: SlackUsersSyncJob,
  ) {}

  @Post('/:syncType')
  async sync(
    @Param('syncType') syncType: SlackSyncType,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    try {
      const { installation } = botCtx;

      this.logger.log(
        `Starting ${syncType} sync for installation ${installation.name}`,
      );

      // Sync the external users
      if (syncType === SlackSyncType.EXTERNAL_USERS) {
        await this.slackExternalUsersSyncJob.execute(installation);
        this.logger.log(
          `Successfully completed ${syncType} sync for installation ${installation.name}`,
        );
      }
      // Sync the internal users
      else if (syncType === SlackSyncType.INTERNAL_USERS) {
        await this.slackUsersSyncJob.execute(installation);
        this.logger.log(
          `Successfully completed ${syncType} sync for installation ${installation.name}`,
        );
      } else {
        throw new InternalServerErrorException(
          `Invalid sync type: ${syncType}`,
        );
      }

      return {
        success: true,
        message: `${syncType} sync completed successfully`,
        syncType,
        installation: installation.name,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';

      this.logger.error(
        `Failed to sync ${syncType} for installation ${botCtx.installation.name}: ${errorMessage}`,
        error instanceof Error ? error.stack : undefined,
      );

      // If the error is an HTTP exception, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      // Otherwise, throw a generic error with the actual error message
      throw new InternalServerErrorException(
        `Failed to sync ${syncType}: ${errorMessage}`,
      );
    }
  }
}
