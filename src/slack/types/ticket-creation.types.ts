import type { Ticket } from '../../platform/interfaces';

/**
 * Enum representing the possible sentinel values for ticket creation operations
 */
export enum TicketCreationResult {
  /** Service is temporarily unavailable */
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  /** General failure occurred during ticket creation */
  GENERAL_FAILURE = 'GENERAL_FAILURE',
}

/**
 * Union type representing all possible return values from ticket creation operations
 */
export type TicketCreationResultType =
  | Ticket // Happy path - ticket created successfully
  | null // Soft failure - triggers UI fallback
  | TicketCreationResult.SERVICE_UNAVAILABLE // Service unavailable - user already notified
  | TicketCreationResult.GENERAL_FAILURE; // General failure - fallback to UI

/**
 * Type guard to check if a ticket creation result is a successful Ticket
 * @param result The result to check
 * @returns True if the result is a Ticket object
 */
export function isTicket(result: TicketCreationResultType): result is Ticket {
  return (
    typeof result === 'object' &&
    result !== null &&
    'id' in result &&
    typeof result.id === 'string'
  );
}
