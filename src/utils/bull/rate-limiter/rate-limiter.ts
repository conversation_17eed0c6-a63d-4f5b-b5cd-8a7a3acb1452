import { InjectQueue } from '@nestjs/bullmq';
import { Injectable, type OnModuleDestroy } from '@nestjs/common';
import { type Queue, QueueEvents, Worker } from 'bullmq';
import type { RateLimiterOptions } from './rate-limiter.types';

@Injectable()
export abstract class AbstractRateLimiter implements OnModuleDestroy {
  private worker: Worker;
  private queueEvents: QueueEvents;
  private defaultLastRequestTime = 0;
  private methodLastRequestTimes: Map<string, number> = new Map();

  constructor(
    @InjectQueue() private readonly queue: Queue,
    private readonly options: RateLimiterOptions,
  ) {
    this.initializeWorker();
    this.initializeQueueEvents();
  }

  /**
   * Setup worker event handlers.
   */
  private setupWorkerEventHandlers(): void {
    this.worker.on('completed', (_job) => {
      // Job completion is handled by the specific worker implementations
      // Removed console.log to reduce noise in production logs
    });

    this.worker.on('failed', (_job, _error) => {
      // Job failures are handled by the specific worker implementations
      // Removed console.warn to reduce noise in production logs
    });

    this.worker.on('error', (_error) => {
      // Worker errors are handled by the specific worker implementations
      // Removed console.error to reduce noise in production logs
    });
  }

  /**
   * Initialize the worker.
   */
  private initializeWorker(): void {
    this.worker = new Worker(
      this.queue.name,
      async (job) => {
        const { fn, args } = job.data;
        await this.enforceRateLimit();
        const result = await this.executeWithRetry(fn, args);
        return result;
      },
      {
        connection: this.queue.opts.connection,
        concurrency: this.options.maxConcurrent || 1,
        limiter: {
          max: this.options.requestsPerSecond,
          duration: 1000,
        },
      },
    );

    this.setupWorkerEventHandlers();
  }

  /**
   * Initialize the queue events.
   */
  private initializeQueueEvents(): void {
    this.queueEvents = new QueueEvents(this.queue.name, {
      connection: this.queue.opts.connection,
    });
  }

  /**
   * Schedule a function to be executed with rate limiting.
   * @param fn The function to execute.
   * @returns The result of the function execution.
   */
  async schedule<T>(fn: () => Promise<T>, method?: string): Promise<T> {
    await this.enforceRateLimit(method);
    return await fn();
  }

  /**
   * Check if the rate limit is exceeded.
   * @param error The error to check.
   * @returns True if the rate limit is exceeded, false otherwise.
   */
  abstract isRateLimited(error: any): boolean | Promise<boolean>;

  /**
   * Execute a function with retry logic.
   * @param fnString The function to execute.
   * @param args The arguments to pass to the function.
   * @param attempt The current retry attempt.
   * @returns The result of the function execution.
   */
  private async executeWithRetry<T>(
    fnString: string,
    args: any[] = [],
    attempt = 1,
  ): Promise<T> {
    try {
      const fn = new Function(`return ${fnString}`);
      return fn(...args);
    } catch (error) {
      if (
        (await this.isRateLimited(error)) &&
        attempt < this.options.retryAttempts
      ) {
        await this.delay(this.options.retryDelay * attempt);
        return this.executeWithRetry(fnString, args, attempt + 1);
      }

      throw error;
    }
  }

  /**
   * Enforce the rate limit by delaying the request if necessary.
   */
  private async enforceRateLimit(method?: string): Promise<void> {
    const now = Date.now();
    const config = method && this.options.methodConfig?.[method];

    if (config) {
      // Use method-specific rate limit
      const lastRequestTime = this.methodLastRequestTimes.get(method) ?? 0;
      const timeSinceLastRequest = now - lastRequestTime;
      const minTimeBetweenRequests = 1000 / config.requestsPerSecond;

      // If the time since the last request is less than the minimum time between requests,
      if (timeSinceLastRequest < minTimeBetweenRequests) {
        const delay = minTimeBetweenRequests - timeSinceLastRequest;
        await this.delay(delay);
      }

      this.methodLastRequestTimes.set(method, Date.now());
    } else {
      // Use default rate limit
      const lastRequestTime = this.defaultLastRequestTime;
      const timeSinceLastRequest = now - lastRequestTime;
      const minTimeBetweenRequests = 1000 / this.options.requestsPerSecond;

      if (timeSinceLastRequest < minTimeBetweenRequests) {
        const delay = minTimeBetweenRequests - timeSinceLastRequest;
        await this.delay(delay);
      }

      this.defaultLastRequestTime = Date.now();
    }
  }

  /**
   * Delay the execution of the next request by the specified number of milliseconds.
   * @param ms The number of milliseconds to delay.
   * @returns A promise that resolves when the delay is over.
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Close the worker and queue events when the module is destroyed.
   */
  async onModuleDestroy() {
    await this.queue.close();
    await this.worker.close();
    await this.queueEvents.close();
  }
}
