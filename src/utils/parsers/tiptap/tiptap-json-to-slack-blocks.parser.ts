import { Repository } from 'typeorm';
import {
  CustomerContacts,
  Installations,
  Users,
} from '../../../database/entities';
import { ILogger } from '../../../utils';
import { PinoLoggerService } from '../../../utils/logger';

// Constants
const SPAN_ID = 'TIPTAP_JSON_TO_SLACK_BLOCKS';
const SERVICE_NAME = 'TiptapJsonToSlackBlocksParser';

// Create a Pino logger instance
const logger: ILogger = new PinoLoggerService('slack-app', SERVICE_NAME);

const convertTiptapMarks = (marks) => {
  if (!marks?.length) {
    return '';
  }
  let finalText = '';

  for (const mark of marks) {
    if (mark.type === 'bold') {
      finalText += '*';
    } else if (mark.type === 'italic') {
      finalText += '_';
    } else if (mark.type === 'strike') {
      finalText += '~';
    }
  }

  return finalText;
};

const convertTiptapLinks = (marks, text) => {
  if (!marks?.length) {
    return '';
  }
  const linkMark = marks.find((mark) => mark.type === 'link');
  const nonLinkMarks = marks.filter((mark) => mark.type !== 'link');

  let finalText = '';
  const nonLinksMarksOpening = convertTiptapMarks(nonLinkMarks);
  const nonLinkMarksClosing = nonLinksMarksOpening.split('').reverse().join('');
  if (linkMark?.attrs?.href) {
    finalText = `${nonLinksMarksOpening}<${linkMark.attrs.href}|${text}>${nonLinkMarksClosing}`;
  }

  return finalText;
};

const convertTiptapText = async (
  text,
  userRepository: Repository<Users>,
  installation: Installations,
  _customerContactRepository: Repository<CustomerContacts>,
  _installationsRepository: Repository<Installations>,
) => {
  if (!text) {
    return '';
  }

  let finalText = '';
  for (const content of text) {
    if (content?.type === 'text') {
      if (!content.marks?.length) {
        finalText += content.text;
      } else if (content.marks.some((mark) => mark.type === 'link')) {
        finalText += convertTiptapLinks(content.marks, content.text);
      } else if (content.marks.some((mark) => mark.type === 'code')) {
        finalText += `\`${content.text.trim()}\``;
      } else {
        finalText += ` ${convertTiptapMarks(content.marks)}${content.text.trim()}${convertTiptapMarks(
          content.marks,
        )
          .split('')
          .reverse()
          .join('')} `;
      }
    } else if (content?.type === 'hardBreak') {
      finalText += '\n';
    } else if (content?.type === 'emoji' && content.attrs?.name) {
      finalText += `:${content.attrs.name}:`;
    } else if (content?.type === 'mention' && content.attrs?.id) {
      const email = content.attrs.email;
      const label = content.attrs.label;

      // Check if the user is already in the database
      const user = await userRepository.findOne({
        where: {
          installation: { id: installation.id },
          slackProfileEmail: email,
        },
      });

      if (user) {
        finalText += `<@${user.slackId}>`;
      } else {
        finalText += `@${label}`;
      }
    }
  }

  return finalText;
};

const convertTiptapToRichText = async (
  content,
  userRepository: Repository<Users>,
  installation: Installations,
  customerContactRepository: Repository<CustomerContacts>,
  installationsRepository: Repository<Installations>,
) => {
  if (!content) {
    return '';
  }
  let finalText: string;
  if (content.type === 'text') {
    if (!content.marks?.length) {
      finalText = content.text;
      return {
        type: 'text',
        text: finalText,
      };
    }
    if (content.marks.some((mark) => mark.type === 'link')) {
      finalText = content.text;
      return {
        type: 'link',
        url: content.marks[0].attrs?.href,
        text: finalText,
        style: {
          bold: content.marks.some((mark) => mark.type === 'bold'),
          italic: content.marks.some((mark) => mark.type === 'italic'),
          strike: content.marks.some((mark) => mark.type === 'strike'),
        },
      };
    }
    if (content.marks.some((mark) => mark.type === 'code')) {
      finalText = content.text.trim();
      return {
        type: 'text',
        text: finalText,
        style: {
          code: true,
        },
      };
    }
    finalText = content.text;
    return {
      type: 'text',
      text: finalText,
      style: {
        bold: content.marks.some((mark) => mark.type === 'bold'),
        italic: content.marks.some((mark) => mark.type === 'italic'),
        strike: content.marks.some((mark) => mark.type === 'strike'),
      },
    };
  }
  if (content.type === 'hardBreak') {
    finalText = '\n';
    return {
      type: 'text',
      text: finalText,
    };
  }
  if (content.type === 'emoji' && content.attrs?.name) {
    finalText = `${content.attrs.name}`;
    return {
      type: 'emoji',
      name: finalText,
    };
  }
  if (content.type === 'mention' && content.attrs?.id) {
    const userEmail = content.attrs.email;
    const label = content.attrs.label;

    // Debug logs for mention processing
    logger.debug(
      `${SPAN_ID} Processing mention: id=${content.attrs.id}, idType=${typeof content.attrs.id}, email=${userEmail}, label=${label}`,
    );

    finalText = `${content.attrs.id}`;

    try {
      logger.debug(
        `Looking up installation with ID: ${installation.id}`,
        SPAN_ID,
      );

      // Query parameters for both user and customer contact
      const queryParams = {
        where: {
          slackProfileEmail: userEmail,
          installation: { id: installation.id },
        },
        relations: ['installation'], // Include the installation relation
      };

      logger.debug(
        `Looking up user by email query: ${JSON.stringify(queryParams)}`,
        SPAN_ID,
      );

      // First try to find a regular user
      const user = await userRepository.findOne(queryParams);

      if (user) {
        logger.debug(
          `User found: slackId=${user.slackId}, email=${user.slackProfileEmail}`,
          SPAN_ID,
        );
        return {
          type: 'user',
          user_id: user.slackId,
        };
      }

      // If no regular user found, check for customer contact
      logger.debug('User not found, checking customer contacts...', SPAN_ID);

      const customerContact =
        await customerContactRepository.findOne(queryParams);

      if (customerContact) {
        logger.debug(
          `Customer contact found: slackId=${customerContact.slackId}, email=${customerContact.slackProfileEmail}`,
          SPAN_ID,
        );
        return {
          type: 'user',
          user_id: customerContact.slackId,
        };
      }

      // No user found with this email
      logger.debug(
        `No user or customer contact found for email: ${userEmail}`,
        SPAN_ID,
      );

      // Add a fallback return to prevent undefined values
      return {
        type: 'text',
        text: label || userEmail || `@${content.attrs.id}` || '',
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      logger.error(
        `Error looking up user by email: ${errorMessage}, email=${userEmail}`,
        errorStack,
        SPAN_ID,
      );

      // Add fallback return in case of error
      return {
        type: 'text',
        text: label || userEmail || `@${content.attrs.id}` || '',
      };
    }
  }
  if (content.type === 'orderedList' || content.type === 'bulletList') {
    return convertListItems(
      content.content,
      content.type === 'orderedList' ? 'ordered' : 'bullet',
      userRepository,
      installation,
      customerContactRepository,
      installationsRepository,
    );
  }

  // Return undefined for unhandled content types
  return undefined;
};

const convertListItems = async (
  content: any[],
  style: string,
  userRepository: Repository<Users>,
  installation: Installations,
  customerContactRepository: Repository<CustomerContacts>,
  installationsRepository: Repository<Installations>,
  indent = 0,
  border = 0,
) => {
  const listBlocks = [];

  for (const listItem of content) {
    const sectionElements = [];

    if (listItem.content) {
      for (const item of listItem.content) {
        if (!item.content) {
          sectionElements.push({
            type: 'text',
            text: '\n\n',
          });
        } else {
          for (const itemContent of item.content) {
            const sectionalElement = await convertTiptapToRichText(
              itemContent,
              userRepository,
              installation,
              customerContactRepository,
              installationsRepository,
            );
            if (sectionalElement) {
              sectionElements.push(sectionalElement);
            }
          }
        }
      }
    }

    if (sectionElements.length > 0) {
      listBlocks.push({
        type: 'rich_text_list',
        style,
        indent,
        border,
        elements: [
          {
            type: 'rich_text_section',
            elements: sectionElements,
          },
        ],
      });
    }
    // Process nested lists
    for (const item of listItem.content) {
      if (item.type === 'bulletList' || item.type === 'orderedList') {
        const nestedListBlock = await convertListItems(
          item.content,
          item.type === 'orderedList' ? 'ordered' : 'bullet',
          userRepository,
          installation,
          customerContactRepository,
          installationsRepository,
          indent + 1,
          border,
        );
        listBlocks.push(...nestedListBlock);
      }
    }
  }

  return listBlocks;
};

export const convertTiptapJSONToSlackBlocks = async (
  tiptapJSON: Record<string, any>,
  userRepository: Repository<Users>,
  installation: Installations,
  customerContactRepository: Repository<CustomerContacts>,
  installationsRepository: Repository<Installations>,
): Promise<any> => {
  logger.debug('Converting TipTap JSON to Slack blocks', SPAN_ID);

  const convertedBlocks: any[] = [];
  if (!tiptapJSON.content) {
    logger.debug('No content in TipTap JSON, returning empty result', SPAN_ID);
    return;
  }

  logger.debug(`Processing ${tiptapJSON.content.length} blocks`, SPAN_ID);

  for (const block of tiptapJSON.content) {
    const text = await convertTiptapText(
      block.content,
      userRepository,
      installation,
      customerContactRepository,
      installationsRepository,
    );
    switch (block.type) {
      case 'heading': {
        if (!block.content) {
          break;
        }

        const listElements = [];
        for (const item of block.content) {
          listElements.push(
            await convertTiptapToRichText(
              item,
              userRepository,
              installation,
              customerContactRepository,
              installationsRepository,
            ),
          );
        }

        convertedBlocks.push({
          type: 'rich_text',
          elements: [
            {
              type: 'rich_text_section',
              elements: listElements,
            },
          ],
        });
        break;
      }

      case 'horizontalRule': {
        convertedBlocks.push({
          type: 'divider',
        });
        break;
      }

      case 'paragraph': {
        if (!block.content) {
          // add 2 line breaks to last listElements
          if (convertedBlocks.length > 0) {
            const lastBlock = convertedBlocks[convertedBlocks.length - 1];
            if (
              lastBlock &&
              lastBlock.type === 'rich_text' &&
              Array.isArray(lastBlock.elements)
            ) {
              let stack = [...lastBlock.elements];
              let lastElementsItem = null;
              while (stack.length > 0) {
                const element = stack.pop();
                if (element.type === 'rich_text_section') {
                  lastElementsItem = element;
                  break;
                }
                if (Array.isArray(element.elements)) {
                  stack = stack.concat(element.elements);
                }
              }

              if (
                lastElementsItem &&
                Array.isArray(lastElementsItem.elements)
              ) {
                lastElementsItem.elements.push({ type: 'text', text: '\n\n' });
              }
            }
          }
          break;
        }

        const listElements = [];
        for (const item of block.content) {
          listElements.push(
            await convertTiptapToRichText(
              item,
              userRepository,
              installation,
              customerContactRepository,
              installationsRepository,
            ),
          );
        }

        convertedBlocks.push({
          type: 'rich_text',
          elements: [
            {
              type: 'rich_text_section',
              elements: listElements,
            },
          ],
        });
        break;
      }

      case 'blockquote': {
        const richTextElements = [];
        if (!block.content) {
          richTextElements.push({
            type: 'rich_text_quote',
            elements: [
              {
                type: 'text',
                text: '\n\n',
              },
            ],
          });
        } else {
          for (const item of block.content) {
            if (item.type === 'bulletList' || item.type === 'orderedList') {
              const listBlock = await convertListItems(
                item.content,
                item.type === 'orderedList' ? 'ordered' : 'bullet',
                userRepository,
                installation,
                customerContactRepository,
                installationsRepository,
                0,
                1,
              );
              richTextElements.push(...listBlock);
            } else if (item.type === 'codeBlock') {
              const listElements = [];
              for (const itemContent of item.content) {
                listElements.push(
                  await convertTiptapToRichText(
                    itemContent,
                    userRepository,
                    installation,
                    customerContactRepository,
                    installationsRepository,
                  ),
                );
              }
              richTextElements.push({
                type: 'rich_text_preformatted',
                border: 1,
                elements: listElements,
              });
            } else {
              const listElements = [];
              if (!item?.content) {
                listElements.push({
                  type: 'text',
                  text: '\n\n',
                });
              } else {
                for (const itemContent of item.content) {
                  listElements.push(
                    await convertTiptapToRichText(
                      itemContent,
                      userRepository,
                      installation,
                      customerContactRepository,
                      installationsRepository,
                    ),
                  );
                }

                richTextElements.push({
                  type: 'rich_text_quote',
                  elements: listElements,
                });
              }
            }
          }
        }
        convertedBlocks.push({
          type: 'rich_text',
          elements: richTextElements,
        });
        break;
      }

      case 'image': {
        convertedBlocks.push({
          type: 'image',
          image_url: block.attrs?.src,
          alt_text: block.attrs?.alt ?? '',
          ...{
            ...(block.attrs?.title && {
              title: {
                emoji: true,
                text: block.attrs.title,
                type: 'plain_text',
              },
            }),
          },
        });
        break;
      }

      case 'video': {
        const videoPayload: Record<string, any> = {
          type: 'video',
          video_url: block.attrs?.src,
        };

        // Check for alternate text
        if (block.attrs?.videoDetails?.alt_text) {
          videoPayload.alt_text = block.attrs.videoDetails.alt_text;
        }

        // Check for title
        if (block.attrs?.videoDetails?.title) {
          videoPayload.title = {
            type: 'plain_text',
            text: block.attrs?.videoDetails?.title || ' ',
            emoji: true,
          };
        }

        // Check for thumbnail
        if (block.attrs?.videoDetails?.thumbnail_url) {
          videoPayload.thumbnail_url = block.attrs.videoDetails.thumbnail_url;
        }

        // Check for title url
        if (block.attrs?.videoDetails?.title_url) {
          videoPayload.title_url = block.attrs.videoDetails.title_url;
        }

        // Check for author name
        if (block.attrs?.videoDetails?.author_name) {
          videoPayload.author_name = block.attrs.videoDetails.author_name;
        }

        // Check for provider name
        if (block.attrs?.videoDetails?.provider_name) {
          videoPayload.provider_name = block.attrs.videoDetails.provider_name;
        }

        // Check for provider icon url
        if (block.attrs?.videoDetails?.provider_icon_url) {
          videoPayload.provider_icon_url =
            block.attrs.videoDetails.provider_icon_url;
        }

        // Check for description
        if (block.attrs?.videoDetails?.description) {
          videoPayload.description = {
            type: 'plain_text',
            text: block.attrs.videoDetails.description,
            emoji: true,
          };
        }

        convertedBlocks.push(videoPayload);
        break;
      }

      case 'textWithImage': {
        convertedBlocks.push({
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: text,
          },
          accessory: {
            type: 'image',
            image_url: block.attrs?.src,
            alt_text: block.attrs?.alt ?? '',
          },
        });
        break;
      }

      case 'textWithButton': {
        convertedBlocks.push({
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: text,
          },
          accessory: {
            type: 'button',
            text: {
              type: 'plain_text',
              text: block.attrs?.buttonText,
              emoji: true,
            },
            url: block.attrs?.href,
            value: 'click_me_123',
            action_id: 'button-action',
          },
        });

        break;
      }

      case 'bulletList':
      case 'orderedList': {
        const listBlock = await convertListItems(
          block.content,
          block.type === 'orderedList' ? 'ordered' : 'bullet',
          userRepository,
          installation,
          customerContactRepository,
          installationsRepository,
        );

        // Make sure we're creating a proper rich_text block with the list elements
        if (listBlock && listBlock.length > 0) {
          convertedBlocks.push({
            type: 'rich_text',
            elements: listBlock,
          });
        }
        break;
      }

      case 'codeBlock': {
        convertedBlocks.push({
          type: 'rich_text',
          elements: [
            {
              type: 'rich_text_preformatted',
              elements: [
                {
                  type: 'text',
                  text,
                },
              ],
            },
          ],
        });

        break;
      }

      case 'files': {
        for (const item of block.content) {
          convertedBlocks.push({
            type: 'rich_text',
            elements: [
              {
                type: 'rich_text_section',
                elements: [
                  {
                    type: 'link',
                    url: item.permalink,
                    text: item.name,
                  },
                ],
              },
            ],
          });
        }
        break;
      }

      default: {
        break;
      }
    }
  }

  return convertedBlocks;
};
