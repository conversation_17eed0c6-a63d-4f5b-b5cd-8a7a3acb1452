/**
 * Custom error classes for Platform API operations
 */

/**
 * Base error class for Platform API operations
 * @param message - Error message describing the issue
 * @param operation - Optional operation context where the error occurred
 */
export class PlatformApiError extends Error {
  constructor(
    message: string,
    public readonly operation?: string,
  ) {
    super(message);
    this.name = 'PlatformApiError';
  }
}

export class PlatformApiUnavailableError extends PlatformApiError {
  constructor(
    message = 'Platform API service is unavailable',
    operation?: string,
  ) {
    super(message, operation);
    this.name = 'PlatformApiUnavailableError';
  }
}

export class PlatformApiTimeoutError extends PlatformApiError {
  constructor(message = 'Platform API request timeout', operation?: string) {
    super(message, operation);
    this.name = 'PlatformApiTimeoutError';
  }
}

export class PlatformApiConnectionError extends PlatformApiError {
  constructor(message = 'Platform API connection error', operation?: string) {
    super(message, operation);
    this.name = 'PlatformApiConnectionError';
  }
}
