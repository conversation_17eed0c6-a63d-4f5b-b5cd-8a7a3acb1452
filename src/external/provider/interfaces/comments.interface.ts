import { FileShareMessageEvent } from '@slack/types';

export interface CreateNewComment {
  content: string;
  ticketId: string;
  htmlContent?: string;
  parentCommentId?: string;
  threadName?: string;
  commentVisibility?: 'public' | 'private';
  customerEmail?: string;
  impersonatedUser<PERSON><PERSON>?: string;
  impersonatedUserName?: string;
  impersonatedUserAvatar?: string;
  channelId: string;
  files: FileShareMessageEvent['files'];
  metadata: {
    ts: string;
    threadTs?: string;
    ignoreSelf: boolean;
    slackThreadLink?: string;
    slackUserId?: string; // Moved from top level for consistency
  };
}

export interface UpdateComment {
  content: string;
  htmlContent?: string;
  commentId: string;
  commentAs: string;
}
