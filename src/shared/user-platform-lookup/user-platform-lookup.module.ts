import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommonModule } from '../../common/common.module';
import { Users } from '../../database/entities';
import { UserPlatformLookupService } from './user-platform-lookup.service';

@Module({
  imports: [CommonModule, TypeOrmModule.forFeature([Users])],
  providers: [UserPlatformLookupService],
  exports: [UserPlatformLookupService],
})
export class UserPlatformLookupModule {}
