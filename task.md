# Code Review Issues - Implementation Plan

## Overview
This document contains code review findings and planned tasks to address identified issues across the codebase. Issues are organized by priority and include specific implementation details.

---

## HIGH PRIORITY TASKS

### 1. Error Handling & HTTP Status Codes

#### Task 1.1: Fix slack-sync.controller.ts (lines 61-63)
**Issue**: Using InternalServerErrorException (5xx) for client-side validation errors
**Impact**: Misrepresents error class and affects monitoring dashboards

**Changes Required**:
```typescript
// BEFORE
throw new InternalServerErrorException(
  `Invalid sync type: ${syncType}`,
);

// AFTER
throw new BadRequestException(
  `Invalid sync type: ${syncType}`,
);
```

**Additional Improvements**:
- Add ParseEnumPipe to route parameter for automatic validation:
```typescript
@Post('/:syncType')
async sync(
  @Param('syncType', new ParseEnumPipe(SlackSyncType)) syncType: SlackSyncType,
  @GetBotCtx() botCtx: BotCtx,
) {
```

**Files to modify**:
- `src/slack/controllers/slack-sync.controller.ts`
- Update imports to include `BadRequestException` and `ParseEnumPipe`

---

### 2. Null Safety & Runtime Error Prevention

#### Task 2.1: Fix on-thread-message.ts (lines 168-189)
**Issue**: Missing guard clause for undefined ticketDetails after API call
**Impact**: Runtime exception when accessing ticketDetails.teamId

**Changes Required**:
```typescript
// Add after the try-catch block (around line 189)
// Bail out if the ticket could not be retrieved
if (!ticketDetails) {
  this.logger.warn(
    `Ticket ${ticketId} not found – skipping thread-message handling.`,
  );
  return;
}
```

**Files to modify**:
- `src/slack/core/on-message/on-thread-message.ts`

---

### 4. Database Transaction Consistency

#### Task 4.1: Fix slack-users-sync.job.ts (lines 121-129)
**Issue**: Sequential upsert operations not wrapped in transaction
**Impact**: Data inconsistency if process crashes between operations

**Changes Required**:
```typescript
// BEFORE
await this.usersRepository.upsert(usersToWrite, {
  conflictPaths: ['slackId', 'installation'],
});
await this.botsRepository.upsert(botsToWrite, {
  conflictPaths: ['slackId', 'installation'],
});
await this.linkUsersToPlatform(installation, usersToWrite);

// AFTER
await this.transactionService.runInTransaction(async (txnContext) => {
  await this.usersRepository.upsertWithTxn(txnContext, usersToWrite, {
    conflictPaths: ['slackId', 'installation'],
  });
  await this.botsRepository.upsertWithTxn(txnContext, botsToWrite, {
    conflictPaths: ['slackId', 'installation'],
  });
});
await this.linkUsersToPlatform(installation, usersToWrite);
```

**Files to modify**:
- `src/slack/processors/jobs/slack-users-sync.job.ts`

---

## MEDIUM PRIORITY TASKS


### 6. API Key Validation Consistency

#### Task 6.1: Fix thena-platform-api.provider.ts (multiple methods)
**Issue**: Inconsistent API key validation across methods
**Impact**: Code duplication and potential validation drift

**Changes Required**:
Replace manual `getApiKey()` checks with `validateApiKey()` calls in methods like:
- `createNewComment`
- `updateComment`
- `updateCommentWithMetadata`
- `getAccountsByDomains`
- `getAccountsByIds`

```typescript
// BEFORE
const apiKey = this.getApiKey(installation.organization);
if (!apiKey) {
  throw new Error('Organization has no API key');
}

// AFTER
this.validateApiKey(installation.organization);
```

**Files to modify**:
- `src/external/provider/thena-platform-api.provider.ts`

---


### 8. Response Body Reading

#### Task 8.1: Fix thena-platform-api.provider.ts (lines 141-146)
**Issue**: Reading response body twice causes second read to fail
**Impact**: Error information may be lost

**Changes Required**:
```typescript
// BEFORE
let responseJson: any;
try {
  responseJson = await response.json();
} catch {
  responseJson = { message: await response.text() };
}

// AFTER (Simplest solution)
const responseText = await response.text();
let responseJson: any;
try {
  responseJson = JSON.parse(responseText);
} catch {
  responseJson = { message: responseText };
}
```

**Files to modify**:
- `src/external/provider/thena-platform-api.provider.ts`

---
