Use BadRequestException (4xx) instead of InternalServerErrorException (5xx) for invalid input

An unknown syncType is a client-side mistake, so returning 500 misrepresents the error class and hampers monitoring dashboards that rely on 5xx rates. Prefer a 400-series exception and let <PERSON><PERSON> do the rest:

-throw new InternalServerErrorException(
-  `Invalid sync type: ${syncType}`,
-);
+throw new BadRequestException(
+  `Invalid sync type: ${syncType}`,
+);
(Optional) add Parse<PERSON>numPipe to the route param to fail fast and avoid manual branching:

@Post('/:syncType')
async sync(
  @Param('syncType', new ParseEnumPipe(SlackSyncType)) syncType: SlackSyncType,
  @GetBotCtx() botCtx: BotCtx,
) {

In src/slack/controllers/slack-sync.controller.ts around lines 61 to 63, replace
the InternalServerErrorException with BadRequestException when the syncType is
invalid, as this is a client error and should return a 4xx status. Additionally,
update the route parameter decorator to use ParseEnumPipe with SlackSyncType to
validate syncType automatically and fail fast, removing the need for manual
error handling.


Comment on lines +837 to +840
    // Get the user ID from the event
    const slackUserId: string | undefined =
      'user' in data ? data.user : undefined;

@coderabbitai coderabbitai bot 12 minutes ago
🧹 Nitpick (assertive)

Minor: inline type annotation is redundant

data.user is already string | undefined per Slack typings; the explicit local type adds verbosity without extra safety.
Feel free to keep for readability if you prefer.

🤖 Prompt for AI Agents
In src/slack/core/on-message/on-thread-message.ts around lines 837 to 840, the
explicit type annotation for slackUserId is redundant because data.user is
already typed as string | undefined by Slack typings. Remove the inline type
annotation from the slackUserId declaration to reduce verbosity while keeping
the assignment logic unchanged.



src/slack/core/on-message/on-thread-message.ts
Comment on lines +168 to +189
    let ticketDetails: Ticket | undefined;
    try {
      ticketDetails = await this.platformApiProvider.getTicket(
        installation,
        ticketId,
      );
    } catch (error) {
      this.logger.error(
        `Failed to get ticket details for ticket ${ticketId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );

      // If the platform API is unavailable, log and return early
      if (error instanceof PlatformApiUnavailableError) {
        this.logger.warn(
          `Platform API service unavailable, skipping thread message processing for ticket ${ticketId}`,
        );
        return;
      }

      // Re-throw other errors
      throw error;
    }
@coderabbitai coderabbitai bot 13 minutes ago
🛠️ Refactor suggestion

Guard against undefined ticket details after fetch failure

If getTicket resolves with undefined (e.g., 404) but does not throw, execution continues and ticketDetails.teamId is accessed later (l. 192), leading to a runtime exception.

-let ticketDetails: Ticket | undefined;
+let ticketDetails: Ticket | undefined;
 ...
-} catch (error) {
+} catch (error) {
   ...
 }
+
+// Bail out if the ticket could not be retrieved
+if (!ticketDetails) {
+  this.logger.warn(
+    `Ticket ${ticketId} not found – skipping thread-message handling.`,
+  );
+  return;
+}
📝 Committable suggestion
‼️ IMPORTANT
Carefully review the code before committing. Ensure that it accurately replaces the highlighted code, contains no missing lines, and has no issues with indentation. Thoroughly test & benchmark the code to ensure it meets the requirements.

Suggested change
    let ticketDetails: Ticket | undefined;
    try {
      ticketDetails = await this.platformApiProvider.getTicket(
        installation,
        ticketId,
      );
    } catch (error) {
      this.logger.error(
        `Failed to get ticket details for ticket ${ticketId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      // If the platform API is unavailable, log and return early
      if (error instanceof PlatformApiUnavailableError) {
        this.logger.warn(
          `Platform API service unavailable, skipping thread message processing for ticket ${ticketId}`,
        );
        return;
      }
      // Re-throw other errors
      throw error;
    }
    let ticketDetails: Ticket | undefined;
    try {
      ticketDetails = await this.platformApiProvider.getTicket(
        installation,
        ticketId,
      );
    } catch (error) {
      this.logger.error(
        `Failed to get ticket details for ticket ${ticketId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      // If the platform API is unavailable, log and return early
      if (error instanceof PlatformApiUnavailableError) {
        this.logger.warn(
          `Platform API service unavailable, skipping thread message processing for ticket ${ticketId}`,
        );
        return;
      }
      // Re-throw other errors
      throw error;
    }
    // Bail out if the ticket could not be retrieved
    if (!ticketDetails) {
      this.logger.warn(
        `Ticket ${ticketId} not found – skipping thread-message handling.`,
      );
      return;
    }
🤖 Prompt for AI Agents
In src/slack/core/on-message/on-thread-message.ts around lines 168 to 189, the
code does not handle the case where getTicket returns undefined without throwing
an error, which causes a runtime exception when accessing ticketDetails.teamId
later. Add a guard after the await call to check if ticketDetails is undefined,
log an appropriate warning or error, and return early to prevent further
processing with undefined ticketDetails.


src/slack/core/on-message/on-thread-message.ts
Comment on lines 834 to +840
    // Enhance the user mentions
    const _enhancedText = enhanceUserMentions(data.text, mentionedSlackUsers);

    // Get the user ID from the event
    const slackUserId: string | undefined =
      'user' in data ? data.user : undefined;

@coderabbitai coderabbitai bot 13 minutes ago
⚠️ Potential issue

_enhancedText computed but never used – potential data loss

You build _enhancedText (l. 835) to replace user-id mentions with real names, but then pass htmlContent as both content and htmlContent in every branch below. The plain-text version is discarded, making mentions appear as raw <@U123> in the platform UI.

Either:

keep content as _enhancedText and htmlContent as is, or
drop the unused variable to silence the linter.
-        content: htmlContent,
+        content: _enhancedText,
         files,
-        htmlContent,
+        htmlContent,
Apply the same adjustment in all three payload branches.

Committable suggestion skipped: line range outside the PR's diff.

🤖 Prompt for AI Agents
In src/slack/core/on-message/on-thread-message.ts around lines 834 to 840, the
variable _enhancedText is computed to replace user-id mentions with real names
but is never used, causing the plain-text content to still show raw mentions. To
fix this, update all three payload branches to assign _enhancedText to the
content field while keeping htmlContent unchanged, ensuring the enhanced text is
used properly. Alternatively, if you decide not to use _enhancedText, remove its
computation to avoid linter warnings.


src/utils/parsers/tiptap/tiptap-json-to-slack-blocks.parser.ts
Comment on lines +639 to +645
              (await convertTiptapText(
                block.content,
                userRepository,
                installation,
                customerContactRepository,
                installationsRepository,
              )) || ' ',
@coderabbitai coderabbitai bot 14 minutes ago
🛠️ Refactor suggestion

Avoid the second expensive convertTiptapText() call

text is already computed for every block at L372-379.
Re-invoking the same async database-hitting routine inside the textWithImage and textWithButton branches doubles the work and DB traffic.

-            text:
-              (await convertTiptapText(
-                block.content,
-                userRepository,
-                installation,
-                customerContactRepository,
-                installationsRepository,
-              )) || ' ',
+            text: text || ' ',
Repeat the same change for the textWithButton branch.

Besides the obvious performance win, this also prevents subtle inconsistencies if the text generation logic ever becomes non-deterministic.

Also applies to: 662-668

🤖 Prompt for AI Agents
In src/utils/parsers/tiptap/tiptap-json-to-slack-blocks.parser.ts around lines
639 to 645 and 662 to 668, the code calls the async function convertTiptapText()
twice for the same block, causing redundant database calls and potential
inconsistencies. To fix this, reuse the previously computed text variable from
lines 372-379 instead of calling convertTiptapText() again in the textWithImage
and textWithButton branches. Replace the second calls with the stored text value
to improve performance and consistency.



src/slack/processors/jobs/slack-users-sync.job.ts
Comment on lines +121 to 129
    // Upsert humans ➜ upsert bots ➜ platform-link & metadata update
    await this.usersRepository.upsert(usersToWrite, {
      conflictPaths: ['slackId', 'installation'],
    });
    await this.botsRepository.upsert(botsToWrite, {
      conflictPaths: ['slackId', 'installation'],
    });
    await this.linkUsersToPlatform(installation, usersToWrite);
  }
@coderabbitai coderabbitai bot 14 minutes ago
🧹 Nitpick (assertive)

Consider wrapping the three sequential steps in a DB transaction

upsert(users) → upsert(bots) → linkUsersToPlatform() are logically one unit.
If the process crashes between steps, the DB may contain humans but no bots (or vice-versa), while the platform might already have linked users. A simple transactional wrapper around the two upserts would make the operation atomic on the DB side.

[KISS transactional pseudo]

await this.usersRepository.manager.transaction(async (mgr) => {
  await mgr.getRepository(Users).upsert(...);
  await mgr.getCustomRepository(BotsRepository).upsert(...);
});
await this.linkUsersToPlatform(installation, usersToWrite);
🤖 Prompt for AI Agents
In src/slack/processors/jobs/slack-users-sync.job.ts around lines 121 to 129,
the sequential calls to upsert users, upsert bots, and link users to the
platform should be wrapped in a database transaction to ensure atomicity.
Refactor the code to use a transaction from the usersRepository manager that
performs both upsert operations inside the transaction callback, so either both
succeed or both fail together. Keep the linkUsersToPlatform call outside the
transaction as it likely involves external platform updates.


src/external/provider/thena-platform-api.provider.ts
Comment on lines +118 to +123
  private validateApiKey(organization: Organizations): void {
    const apiKey = this.getApiKey(organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }
  }
@coderabbitai coderabbitai bot 15 minutes ago
🧹 Nitpick (assertive)

validateApiKey is under-used – enforce it consistently

Introducing validateApiKey is great, but many methods (e.g. createNewComment, updateComment) still call getApiKey & repeat the same guard. Consider replacing those manual checks with a single validateApiKey call to avoid duplication and future drift.

🤖 Prompt for AI Agents
In src/external/provider/thena-platform-api.provider.ts around lines 118 to 123,
the validateApiKey method is defined but not consistently used across methods
like createNewComment and updateComment, which still call getApiKey and perform
their own checks. Refactor those methods to remove direct getApiKey calls and
instead call validateApiKey to enforce the API key presence check uniformly,
eliminating duplicated guard logic and reducing risk of inconsistent validation.
@dhruv-thena	Reply...
src/external/provider/thena-platform-api.provider.ts
Comment on lines +348 to +352
    // Merge custom headers with default headers
    const headers = customHeaders
      ? { ...customHeaders, ...defaultHeaders }
      : defaultHeaders;

@coderabbitai coderabbitai bot 15 minutes ago
🛠️ Refactor suggestion

Custom headers are unintentionally overridden

By spreading customHeaders first, any key that overlaps with defaults (Content-Type, etc.) is overwritten by the defaults.
If callers need to override a default (e.g. multipart upload), place defaultHeaders first:

-    const headers = customHeaders
-      ? { ...customHeaders, ...defaultHeaders }
-      : defaultHeaders;
+    const headers = customHeaders
+      ? { ...defaultHeaders, ...customHeaders }
+      : defaultHeaders;
📝 Committable suggestion
🤖 Prompt for AI Agents
@dhruv-thena	Reply...
src/external/provider/thena-platform-api.provider.ts
Comment on lines +695 to +702
    // Create X-User-ID headers if Slack user ID is provided
    let customHeaders: Record<string, string> = {};
    try {
      customHeaders = await this.createXUserIdHeaders(
        data.metadata.slackUserId,
        installation,
      );
    } catch (error) {
@coderabbitai coderabbitai bot 15 minutes ago
🧹 Nitpick (assertive)

Error path swallows stack trace

When createXUserIdHeaders throws, the catch block logs only error.message, discarding the stack.
Including error.stack (when present) eases debugging:

-      const errorMessage =
-        error instanceof Error ? error.message : 'Unknown error';
+      const errorMessage =
+        error instanceof Error ? `${error.message}\n${error.stack ?? ''}` : 'Unknown error';
Committable suggestion skipped: line range outside the PR's diff.

🤖 Prompt for AI Agents
In src/external/provider/thena-platform-api.provider.ts around lines 695 to 702,
the catch block for errors thrown by createXUserIdHeaders currently logs only
error.message, which loses the stack trace. Modify the catch block to log
error.stack if available, or fallback to error.message, so that the full error
context is preserved for easier debugging.
@dhruv-thena	Reply...
src/external/provider/thena-platform-api.provider.ts
@@ -453,7 +667,7 @@ export class ThenaPlatformApiProvider {
    const files = data.files;
    const uploadedFiles = await this.uploadToPlatform(installation, files);

    const attachmentIds = uploadedFiles.map((data) => data?.data?.uid);
    const attachmentIds = uploadedFiles.filter(Boolean).map((f) => f?.uid);
@coderabbitai coderabbitai bot 15 minutes ago
🧹 Nitpick (assertive)

TypeScript can’t infer non-null after filter(Boolean)

uploadedFiles.filter(Boolean) removes falsy values, but the compiler still sees f as possibly null | undefined.
Use a type-guard or a non-null assertion to avoid the optional-chaining every access:

-const attachmentIds = uploadedFiles.filter(Boolean).map((f) => f?.uid);
+const attachmentIds = uploadedFiles
+  .filter((f): f is { uid: string } => Boolean(f))
+  .map((f) => f.uid);
📝 Committable suggestion
‼️ IMPORTANT
Carefully review the code before committing. Ensure that it accurately replaces the highlighted code, contains no missing lines, and has no issues with indentation. Thoroughly test & benchmark the code to ensure it meets the requirements.

Suggested change
    const attachmentIds = uploadedFiles.filter(Boolean).map((f) => f?.uid);
    const attachmentIds = uploadedFiles
      .filter((f): f is { uid: string } => Boolean(f))
      .map((f) => f.uid);
🤖 Prompt for AI Agents
In src/external/provider/thena-platform-api.provider.ts at line 670, TypeScript
does not narrow the type of elements after using filter(Boolean), so f is still
considered possibly null or undefined. To fix this, replace the filter(Boolean)
with a type-guard function that explicitly asserts the value is not null or
undefined, or use a non-null assertion to tell TypeScript that f is definitely
defined before mapping to f.uid, removing the need for optional chaining.
@dhruv-thena	Reply...
src/external/provider/thena-platform-api.provider.ts
Comment on lines +141 to +146
    let responseJson: any;
    try {
      responseJson = await response.json();
    } catch {
      responseJson = { message: await response.text() };
    }
@coderabbitai coderabbitai bot 15 minutes ago
⚠️ Potential issue

Reading the response body twice can swallow the real error

await response.json() consumes the body stream. If the JSON parse fails, await response.text() will always return an empty string because the stream is already read.
Guard with a content-type check before parsing or use response.clone():

-    let responseJson: any;
-    try {
-      responseJson = await response.json();
-    } catch {
-      responseJson = { message: await response.text() };
-    }
+    let responseJson: any;
+    const isJson =
+      response.headers.get('content-type')?.includes('application/json');
+    if (isJson) {
+      try {
+        responseJson = await response.json();
+      } catch {
+        // fall back to text
+        responseJson = { message: await response.text() };
+      }
+    } else {
+      responseJson = { message: await response.text() };
+    }
📝 Committable suggestion
‼️ IMPORTANT
Carefully review the code before committing. Ensure that it accurately replaces the highlighted code, contains no missing lines, and has no issues with indentation. Thoroughly test & benchmark the code to ensure it meets the requirements.

Suggested change
    let responseJson: any;
    try {
      responseJson = await response.json();
    } catch {
      responseJson = { message: await response.text() };
    }
    let responseJson: any;
    const isJson =
      response.headers.get('content-type')?.includes('application/json');
    if (isJson) {
      try {
        responseJson = await response.json();
      } catch {
        // fall back to text
        responseJson = { message: await response.text() };
      }
    } else {
      responseJson = { message: await response.text() };
    }
🤖 Prompt for AI Agents
In src/external/provider/thena-platform-api.provider.ts around lines 141 to 146,
the code reads the response body twice by calling response.json() and then
response.text(), which causes the second read to fail because the stream is
already consumed. To fix this, check the Content-Type header before parsing to
decide whether to call response.json() or response.text(), or alternatively use
response.clone() to create a copy of the response stream before the first read,
so the second read can consume the clone without error.


